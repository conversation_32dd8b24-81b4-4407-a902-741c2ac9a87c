
import os
from datetime import datetime
from docx import Document
from docx.enum.text import WD_ALIGN_PARAGRAPH

# This data was manually parsed from the web fetch results.
tenders_data = [
    {
        "tender_id": "6938",
        "reference_number": "MOHCC-CT-029-2024",
        "title": "Supply and Erection of fencing at Health Posts",
        "category_name": "Fencing Services",
        "procuring_entity": "MINISTRY OF HEALTH AND CHILD CARE",
        "scope": "Open",
        "publish_date": "30-Aug-2024 04:00 PM",
        "closing_date": "21-Aug-2025 10:00 AM"
    },
    {
        "tender_id": "8615",
        "reference_number": "MOHCC-CT-028-2024",
        "title": "Supply, installation and commissioning of Solar system at various Hospital facilities.",
        "category_name": "Solar Panels and Accessories",
        "procuring_entity": "MINISTRY OF HEALTH AND CHILD CARE",
        "scope": "Open",
        "publish_date": "11-Oct-2024 10:24 AM",
        "closing_date": "21-Aug-2025 10:00 AM"
    },
    {
        "tender_id": "22218",
        "reference_number": "GZU/ST/06/2025",
        "title": "Stationery",
        "category_name": "Stationery Products and Paper Raw Materials",
        "procuring_entity": "GREAT ZIMBABWE UNIVERSITY",
        "scope": "Open",
        "publish_date": "24-Apr-2025 06:03 PM",
        "closing_date": "18-Jul-2025 10:00 AM"
    },
    {
        "tender_id": "22418",
        "reference_number": "CMED01/INT/2025",
        "title": "SUPPLY AND DELIVERY OF CONSTRUCTION EQUIPMENT",
        "category_name": "New Plant and Equipment",
        "procuring_entity": "CENTRAL MECHANICAL AND EQUIPMENT DEPARTMENT",
        "scope": "Open",
        "publish_date": "24-Apr-2025 04:30 PM",
        "closing_date": "01-Jul-2025 10:00 AM"
    },
    {
        "tender_id": "22482",
        "reference_number": "ZINWA/GOO/2025/102",
        "title": "Financing, supply, delivery, installation and commissioning of Prepaid Water Meters, Token Generation System and Smart Water Meter Monitoring System",
        "category_name": "Pre-paid Meters (electricty, Water etc)",
        "procuring_entity": "ZIMBABWE NATIONAL WATER AUTHORITY",
        "scope": "Open",
        "publish_date": "25-Apr-2025 05:00 PM",
        "closing_date": "04-Jul-2025 10:00 AM"
    },
    {
        "tender_id": "23129",
        "reference_number": "ZPC/HO/INT 10/2025",
        "title": "PROVISION OF COOLING TOWER 1 AND 2 INTERNAL REFURBISHMENT SERVICES",
        "category_name": "Construction and Civil Works (buildings, dams, roads etc.) (New),Plant and Equipment Maintenance",
        "procuring_entity": "ZIMBABWE POWER COMPANY",
        "scope": "Open",
        "publish_date": "06-May-2025 09:00 AM",
        "closing_date": "02-Jul-2025 05:00 PM"
    },
    {
        "tender_id": "23368",
        "reference_number": "MOHCC-CT-02-2025",
        "title": "SUPPLY AND DELIVERY OF CORPORATE WEAR FOR PORT HEALTH OFFICERS/DRIVERS AND OFFICE ORDERLY",
        "category_name": "Corporate Wear",
        "procuring_entity": "MINISTRY OF HEALTH AND CHILD CARE",
        "scope": "Open",
        "publish_date": "16-May-2025 11:30 PM",
        "closing_date": "07-Jul-2025 11:00 PM"
    },
    {
        "tender_id": "23426",
        "reference_number": "ZPC/HO/INT14/2024",
        "title": "SUPPLY AND DELIVERY OF QUICK STAGE SCAFFOLDING MATERIAL AT HWANGE POWER STATION (RETENDER)",
        "category_name": "New Plant and Equipment",
        "procuring_entity": "ZIMBABWE POWER COMPANY",
        "scope": "Open",
        "publish_date": "08-May-2025 06:30 PM",
        "closing_date": "02-Jul-2025 05:00 PM"
    },
    {
        "tender_id": "23456",
        "reference_number": "MOJLPA/03/2025",
        "title": "The Supply and Delivery of Motor Vehicles",
        "category_name": "New Light Motor Vehicles",
        "procuring_entity": "MINISTRY OF JUSTICE LEGAL AND PARLIAMENTARY AFFAIRS",
        "scope": "Open",
        "publish_date": "09-May-2025 04:30 PM",
        "closing_date": "04-Jul-2025 10:00 AM"
    },
    {
        "tender_id": "23461",
        "reference_number": "ZH-ITB-42-2025",
        "title": "SUPPLY AND DELIVERY OF OFFICE FURNITURE",
        "category_name": "Furniture, Office Equipment, Upholstery, Carpeting & Curtaining Products",
        "procuring_entity": "ZESA HOLDINGS",
        "scope": "Open",
        "publish_date": "08-May-2025 06:00 PM",
        "closing_date": "01-Jul-2025 10:00 AM"
    },
    {
        "tender_id": "23549",
        "reference_number": "OAG048/2025",
        "title": "SUPPLY AND DELIVERY OF4 X 4 DOUBLE CABS MOTOR VEHICLES",
        "category_name": "New Light Motor Vehicles",
        "procuring_entity": "OFFICE OF THE AUDITOR GENERAL",
        "scope": "Open",
        "publish_date": "09-May-2025 01:00 PM",
        "closing_date": "04-Jul-2025 03:30 PM"
    },
    {
        "tender_id": "23594",
        "reference_number": "MOJLPA/04/2025",
        "title": "Supply and Delivery of Buses",
        "category_name": "New Heavy Motor Vehicles & Buses",
        "procuring_entity": "MINISTRY OF JUSTICE LEGAL AND PARLIAMENTARY AFFAIRS",
        "scope": "Open",
        "publish_date": "09-May-2025 04:30 PM",
        "closing_date": "04-Jul-2025 10:00 AM"
    },
    {
        "tender_id": "23814",
        "reference_number": "NOIC/ME/LPLS/60/2025",
        "title": "SUPPLY, AND INSTALLATION OF LED LIGHTING SYSTEM FOR NOIC BEITBRIDGE DEPOT",
        "category_name": "Installation, Repair & Maintenance of Electrical Equipment, Generators, Power Back-Up Equipment & Miscellaneous Electrical Repair Works,Electrical Products: Cables and Materials, Power Back-Up Equipment, Transformers, Standby Generators, Consumables & Accessories",
        "procuring_entity": "NATIONAL OIL INFRASTRUCTURE COMPANY OF ZIMBABWE PRIVATE LIMITED COMPANY",
        "scope": "Open",
        "publish_date": "13-May-2025 09:00 AM",
        "closing_date": "04-Jul-2025 10:00 AM"
    },
    {
        "tender_id": "23820",
        "reference_number": "NOIC/ME/FSP/88/2025",
        "title": "PROVISION OF FIRE SYSTEM UPGRADE WORKS FOR NOIC BULAWAYO DEPOT",
        "category_name": "Fire Fighting Equipment Maintenance,Fire Fighting Equipment",
        "procuring_entity": "NATIONAL OIL INFRASTRUCTURE COMPANY OF ZIMBABWE PRIVATE LIMITED COMPANY",
        "scope": "Open",
        "publish_date": "13-May-2025 10:40 AM",
        "closing_date": "07-Jul-2025 10:00 AM"
    },
    {
        "tender_id": "23821",
        "reference_number": "NOIC/ME/LM/50/2025",
        "title": "SUPPLY , DELIVERY, INSTALLATION AND COMMISSIONING OF PETROL BLENDING METERS",
        "category_name": "Plant and Equipment Maintenance,New Plant and Equipment",
        "procuring_entity": "NATIONAL OIL INFRASTRUCTURE COMPANY OF ZIMBABWE PRIVATE LIMITED COMPANY",
        "scope": "Open",
        "publish_date": "13-May-2025 09:15 AM",
        "closing_date": "08-Jul-2025 10:00 AM"
    },
    {
        "tender_id": "23828",
        "reference_number": "COB/WKD/BM/01/2025",
        "title": "SUPPLY AND DELIVERY OF BUILDING MATERIAL",
        "category_name": "Tools and Hardware",
        "procuring_entity": "CITY OF BULAWAYO",
        "scope": "Open",
        "publish_date": "13-May-2025 05:00 PM",
        "closing_date": "15-Jul-2025 10:00 AM"
    },
    {
        "tender_id": "23957",
        "reference_number": "NOIC/ME/FDP/19/2025",
        "title": "Construction of Dipping Point at Beitbridge Depot",
        "category_name": "Construction and Civil Works (buildings, dams, roads etc.) (New)",
        "procuring_entity": "NATIONAL OIL INFRASTRUCTURE COMPANY OF ZIMBABWE PRIVATE LIMITED COMPANY",
        "scope": "Open",
        "publish_date": "13-May-2025 05:31 PM",
        "closing_date": "04-Jul-2025 10:00 AM"
    },
    {
        "tender_id": "24177",
        "reference_number": "myedvt/017/2025",
        "title": "new motorcycles",
        "category_name": "New Motor Cycles",
        "procuring_entity": "MINISTRY OF YOUTH EMPOWERMENT DEVELOPMENT AND VOCATIONAL TRAINING",
        "scope": "Open",
        "publish_date": "15-May-2025 04:10 PM",
        "closing_date": "08-Jul-2025 10:00 AM"
    },
    {
        "tender_id": "24195",
        "reference_number": "ZETDC/DOM/35/2025",
        "title": "Supply and Delivery of Cable Fault Locater",
        "category_name": "Electrical Products: Cables and Materials, Power Back-Up Equipment, Transformers, Standby Generators, Consumables & Accessories",
        "procuring_entity": "ZIMBABWE ELECTRICITY TRANSMISSION AND DISTRIBUTION COMPANY",
        "scope": "Open",
        "publish_date": "23-May-2025 04:41 PM",
        "closing_date": "01-Jul-2025 10:00 AM"
    },
    {
        "tender_id": "24290",
        "reference_number": "TDH/GE/03/25",
        "title": "SUPPLY, CONSTRUCTION AND COMMISSIONING OF SEWER AND WATER WORKS",
        "category_name": "Mechanical and Structural Engineering (incl. steel fabrication, steel roof and coverings, machining, sewage management, dewatering, etc.),Water And Sewer Engineering and Utilities",
        "procuring_entity": "TSHOLOTSHO DISTRICT HOSPITAL",
        "scope": "Open",
        "publish_date": "02-Jun-2025 11:00 AM",
        "closing_date": "08-Aug-2025 10:30 AM"
    }
]

def create_tender_report(output_dir="."):
    """Create a comprehensive summary report with detailed tender information."""
    os.makedirs(output_dir, exist_ok=True)
    
    doc = Document()
    
    # Main Title
    title = doc.add_heading('Zimbabwe eGP Latest Tenders', 0)
    title.alignment = WD_ALIGN_PARAGRAPH.CENTER
    doc.add_paragraph(f"Report Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

    # --- Latest Tenders Summary Table ---
    doc.add_heading('Latest Tenders Summary', level=1)
    if tenders_data:
        table = doc.add_table(rows=1, cols=4)
        table.style = 'Table Grid'
        headers = table.rows[0].cells
        headers[0].text = 'Tender ID'
        headers[1].text = 'Tender Title'
        headers[2].text = 'Procuring Entity'
        headers[3].text = 'Closing Date'

        for tender in tenders_data:
            row = table.add_row().cells
            row[0].text = tender.get('tender_id', 'N/A')
            row[1].text = tender.get('title', 'N/A')
            row[2].text = tender.get('procuring_entity', 'N/A')
            row[3].text = tender.get('closing_date', 'N/A')
    else:
        doc.add_paragraph("No tender data was available to generate a summary table.")

    # --- Detailed Tender Information ---
    doc.add_heading('Detailed Tender Information', level=1)
    if tenders_data:
        for i, tender in enumerate(tenders_data, 1):
            doc.add_heading(f"Tender {i}: {tender.get('title', 'N/A')}", level=2)
            for key, value in tender.items():
                # Use a simple string format for the paragraph
                p = doc.add_paragraph()
                p.add_run(f"{key.replace('_', ' ').title()}: ").bold = True
                p.add_run(str(value))
    else:
        doc.add_paragraph("No detailed tender information available.")

    # Save report
    filename = f"Zimbabwe_Tender_Data_{datetime.now().strftime('%Y%m%d_%H%M%S')}.docx"
    filepath = os.path.join(output_dir, filename)
    doc.save(filepath)
    
    print(f"Detailed tender report saved: {filepath}")
    return filepath

if __name__ == "__main__":
    create_tender_report()
