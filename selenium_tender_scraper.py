from selenium.webdriver.support.ui import Web<PERSON><PERSON><PERSON>ait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException # Import TimeoutException
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.service import Service
import time
import os
from datetime import datetime
from docx import Document
from docx.enum.text import WD_ALIGN_PARAGRAPH

class SeleniumTenderScraper:
    def __init__(self):
        self.tender_data = []
        self.base_url = "https://egp.praz.org.zw/egp-SW5kZXhlcy9pbmRleA"
        
        # Path to your chromedriver.exe
        # Make sure this path is correct for your system
        self.chromedriver_path = r"C:\Users\<USER>\Downloads\zimbabwe_tenders\chromedriver-win64\chromedriver.exe"
        
        # Setup Chrome options to run headless (without opening a browser window)
        chrome_options = webdriver.ChromeOptions()
        chrome_options.add_argument("--headless")
        chrome_options.add_argument("--disable-gpu")
        chrome_options.add_argument("--no-sandbox")
        
        service = Service(self.chromedriver_path)
        self.driver = webdriver.Chrome(service=service, options=chrome_options)
        
    def scrape_tenders(self):
        print("Starting Selenium scraping...")
        self.driver.get(self.base_url)
        print(f"Navigated to {self.base_url}. Waiting for initial load...")
        time.sleep(20) # Increased initial wait time significantly
        print("Initial wait complete.")

        # Save page source for debugging
        with open("page_source.html", "w", encoding="utf-8") as f:
            f.write(self.driver.page_source)
        print("Page source saved to page_source.html for debugging.")
        
        page_count = 0
        while page_count < 3: # Limit to 3 pages for demo
            print(f"Processing page {page_count + 1}...")
            try:
                print("Waiting for table with class 'table-bordered' to be present...")
                # Wait for the table to be present by its class name
                WebDriverWait(self.driver, 30).until(
                    EC.presence_of_element_located((By.CLASS_NAME, "table-bordered"))
                )
                print("Table with class 'table-bordered' found. Searching for specific tender table...")
                
                tender_table = None
                tables = self.driver.find_elements(By.CLASS_NAME, "table-bordered")
                print(f"Found {len(tables)} table(s) with class 'table-bordered' on the page.")
                for table in tables:
                    try:
                        # Check headers within the thead section
                        header_elements = table.find_elements(By.XPATH, ".//thead/tr/th")
                        header_texts = [h.text.strip().lower() for h in header_elements]
                        
                        # Check for a subset of expected headers to identify the correct table
                        expected_headers = ["tender id", "tender reference number", "tender title", "procuring entity", "publish date", "closing date"]
                        if all(header in header_texts for header in expected_headers):
                            tender_table = table
                            print("Identified tender table by headers in thead.")
                            break
                    except Exception as e:
                        # Ignore tables that don't have a thead or th elements
                        pass
                
                if not tender_table:
                    print("No tender table found on current page, ending scraping.")
                    break

                rows = tender_table.find_elements(By.XPATH, ".//tbody/tr")
                print(f"Found {len(rows)} rows in the tender table.")
                if not rows:
                    print("No tender rows found on current page, ending scraping.")
                    break

                for i, row in enumerate(rows):
                    try:
                        cols = row.find_elements(By.TAG_NAME, "td")
                        if len(cols) >= 7: # Ensure enough columns exist
                            tender = {
                                "tender_id": cols[0].text.strip(),
                                "reference_number": cols[1].text.strip(),
                                "title": cols[2].text.strip(),
                                "category_name": cols[3].text.strip(),
                                "procuring_entity": cols[4].text.strip(),
                                "scope": cols[5].text.strip(),
                                "publish_date": cols[6].text.strip(),
                                "closing_date": cols[7].text.strip() if len(cols) > 7 else "N/A" # Closing date might be missing
                            }
                            self.tender_data.append(tender)
                            # print(f"  Extracted tender: {tender.get('title', 'N/A')}") # Uncomment for verbose tender extraction
                        else:
                            print(f"  Skipping row {i}: Not enough columns ({len(cols)} found).")
                    except Exception as e:
                        print(f"Error extracting row data for row {i}: {e}")
                
                page_count += 1
                print(f"Finished processing page {page_count}.")
                # Try to find and click the 'Next' button
                if page_count >= 3:
                    print("Demo limit reached. Finishing scraping.")
                    break

                print("Waiting for 'next >' button to be clickable...")
                next_button = WebDriverWait(self.driver, 10).until(
                    EC.element_to_be_clickable((By.XPATH, "//a[text()='next >']"))
                )
                if "disabled" in next_button.get_attribute("class"):
                    print("'next >' button is disabled. No more pages. Finishing scraping.")
                    break
                print("Clicking 'next >' button...")
                next_button.click()
                time.sleep(3) # Wait for the next page to load
                print("Page navigation complete.")
            except TimeoutException:
                print("Timeout: Table or next button not found within the expected time. The page might not have loaded correctly or the XPath is incorrect.")
                break
            except Exception as e:
                print(f"An unexpected error occurred during page processing or navigation: {e}")
                break
                
        self.driver.quit()
        print(f"Scraping finished. Found {len(self.tender_data)} tenders.")
        return self.tender_data
                
        self.driver.quit()
        print(f"Scraping finished. Found {len(self.tender_data)} tenders.")
        return self.tender_data

    def create_tender_report(self, output_dir="zimbabwe_tenders"):
        """Create a comprehensive summary report with detailed tender information."""
        os.makedirs(output_dir, exist_ok=True)
        
        doc = Document()
        
        # Main Title
        title = doc.add_heading('Zimbabwe eGP Latest Tenders', 0)
        title.alignment = WD_ALIGN_PARAGRAPH.CENTER
        doc.add_paragraph(f"Report Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

        # --- Latest Tenders Summary Table ---
        doc.add_heading('Latest Tenders Summary', level=1)
        if self.tender_data:
            table = doc.add_table(rows=1, cols=4)
            table.style = 'Table Grid'
            headers = table.rows[0].cells
            headers[0].text = 'Tender ID'
            headers[1].text = 'Tender Title'
            headers[2].text = 'Procuring Entity'
            headers[3].text = 'Closing Date'

            for tender in self.tender_data:
                row = table.add_row().cells
                row[0].text = tender.get('tender_id', 'N/A')
                row[1].text = tender.get('title', 'N/A')
                row[2].text = tender.get('procuring_entity', 'N/A')
                row[3].text = tender.get('closing_date', 'N/A')
        else:
            doc.add_paragraph("No tender data was extracted to generate a summary table.")


        # --- Detailed Tender Information ---
        doc.add_heading('Detailed Tender Information', level=1)
        if self.tender_data:
            for i, tender in enumerate(self.tender_data, 1):
                doc.add_heading(f"Tender {i}: {tender.get('title', 'N/A')}", level=2)
                for key, value in tender.items():
                    p = doc.add_paragraph()
                    p.add_run(f"{key.replace('_', ' ').title()}: ").bold = True
                    p.add_run(str(value))
        else:
            doc.add_paragraph("No detailed tender information available.")

        # Save report
        filename = f"Zimbabwe_Tender_Data_Selenium_{datetime.now().strftime('%Y%m%d_%H%M%S')}.docx"
        filepath = os.path.join(output_dir, filename)
        doc.save(filepath)
        
        print(f"Detailed tender report saved: {filepath}")
        return filepath

def main():
    scraper = SeleniumTenderScraper()
    tenders = scraper.scrape_tenders()
    if tenders:
        scraper.create_tender_report()
    else:
        print("No tenders found to report.")

if __name__ == "__main__":
    main()
