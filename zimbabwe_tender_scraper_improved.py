"""
Zimbabwe Tender Documentation System - Improved Version
Handles broken URLs, validates content, and provides detailed status reporting
"""

from firecrawl import <PERSON><PERSON><PERSON>lApp
from docx import Document
from docx.enum.text import WD_ALIGN_PARAGRAPH
import json
import os
from datetime import datetime
import re
import time

app = FirecrawlApp(api_key="fc-608eb9468622451c94f984c900182a03")

class ImprovedZimbabweTenderScraper:
    def __init__(self):
        self.tender_data = []
        self.failed_urls = []
        self.broken_urls = []
        self.no_tender_content = []
        self.processed_count = 0
        
        # Updated Zimbabwe procurement portals with alternative URLs where available
        self.entries = [
            ("Zimbabwe eGovernment Procurement (eGP) Portal", "https://egp.praz.org.zw/egp-SW5kZXhlcy9pbmRleA")
        ]
    
    def validate_url_content(self, url):
        """Check if URL has valid tender-related content"""
        try:
            result = app.scrape_url(url, formats=['markdown'])
            
            if not result.success:
                return "FAILED_TO_SCRAPE", None, 0
            
            content = result.markdown
            content_length = len(content)
            
            # Check for common error indicators
            error_indicators = [
                "404", "not found", "page not found", "no results found",
                "oops", "error", "page was not found", "does not exist",
                "could not be found", "try refining your search"
            ]
            
            content_lower = content.lower()
            
            for indicator in error_indicators:
                if indicator in content_lower:
                    return "BROKEN_URL", content, content_length
            
            # Check if content is too short (likely error page)
            if content_length < 150:
                return "INSUFFICIENT_CONTENT", content, content_length
            
            # Check for tender-related keywords
            tender_keywords = [
                "tender", "procurement", "bid", "contract", "rfp", "rfq",
                "notice", "invitation", "proposal", "quotation", "award"
            ]
            
            has_tender_keywords = any(keyword in content_lower for keyword in tender_keywords)
            
            if has_tender_keywords:
                return "POTENTIAL_TENDER_CONTENT", content, content_length
            else:
                return "NO_TENDER_CONTENT", content, content_length
                
        except Exception as e:
            return "ERROR", str(e), 0
    
    def extract_tender_data(self, name, url):
        """Extract tender information with improved validation"""
        print(f"Processing: {name}")
        print(f"URL: {url}")
        
        # First validate the URL
        status, content, content_length = self.validate_url_content(url)
        
        print(f"Content Status: {status}")
        print(f"Content Length: {content_length} characters")
        
        if status in ["FAILED_TO_SCRAPE", "ERROR"]:
            print(f"Status: FAILED - {content}")
            self.failed_urls.append((name, url, status))
            return None
        
        elif status == "BROKEN_URL":
            print("Status: BROKEN - URL returns error page")
            self.broken_urls.append((name, url, "Broken or moved"))
            return None
        
        elif status == "INSUFFICIENT_CONTENT":
            print("Status: INSUFFICIENT - Content too short")
            self.no_tender_content.append((name, url, "Insufficient content"))
            return None
        
        elif status == "NO_TENDER_CONTENT":
            print("Status: NO_TENDER_CONTENT - No procurement keywords found")
            self.no_tender_content.append((name, url, "No tender keywords"))
            
            # Still try to extract general information
            general_info = self.extract_general_info(name, url, content)
            return general_info
        
        elif status == "POTENTIAL_TENDER_CONTENT":
            print("Status: POTENTIAL_CONTENT - Found tender keywords")
            
            # Try detailed tender extraction
            tender_info = self.extract_detailed_tender_info(name, url)
            if tender_info:
                return tender_info
            else:
                # Fallback to general info
                return self.extract_general_info(name, url, content)
        
        return None
    
    def extract_detailed_tender_info(self, name, url):
        """Extract detailed tender information using AI."""
        try:
            tender_prompt = '''
            Extract a list of all tenders from the page. For each tender, provide the following details in a structured JSON format within a `data` field:
            - tender_id: The unique ID for the tender.
            - reference_number: The official reference number.
            - title: The title of the tender.
            - procuring_entity: The name of the procuring entity.
            - scope: The scope of the tender.
            - publish_date: The date the tender was published.
            - closing_date: The closing date for the tender.
            '''
            
            result = app.scrape_url(
                url,
                formats=["json"],
                json_options={"prompt": tender_prompt}
            )
            
            if result.success and hasattr(result, 'json') and isinstance(result.json, dict) and 'data' in result.json and isinstance(result.json['data'], list):
                tenders = result.json['data']
                if tenders:
                    print(f"Status: SUCCESS - Extracted {len(tenders)} tenders.")
                    return {
                        'source_name': name,
                        'source_url': url,
                        'scraped_date': datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                        'extraction_type': 'detailed_tender',
                        'data': tenders
                    }
                else:
                    print("Status: NO_TENDERS_FOUND - The query returned no tenders.")
                    return None
            else:
                print("Status: EXTRACTION_FAILED - The AI did not return the expected data structure.")
                return None
                
        except Exception as e:
            print(f"Status: ERROR - {e}")
            return None
    
    def extract_general_info(self, name, url, content):
        """Extract general information when no specific tenders found"""
        try:
            general_prompt = f"""
            This appears to be a government or institutional website: {name}
            
            Extract general information about this organization:
            - organization: Full organization name
            - description: What this organization does
            - contact_info: Any contact information found
            - website_status: Brief description of what's on this page
            - procurement_process: Any general procurement information mentioned
            
            Based on the content provided, give a brief summary of this organization and any procurement-related information available.
            """
            
            result = app.scrape_url(
                url,
                formats=["json"],
                json_options={"prompt": general_prompt}
            )
            
            if result.success and hasattr(result, 'json'):
                info = result.json
                info['source_name'] = name
                info['source_url'] = url
                info['scraped_date'] = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                info['extraction_type'] = 'general_info'
                info['title'] = f"General Information - {name}"
                
                print(f"Status: GENERAL_INFO - {info.get('organization', 'Organization info')}")
                return info
            else:
                return None
                
        except Exception as e:
            print(f"Error extracting general info: {e}")
            return None
    
    def process_all_sources(self):
        """Process all sources with improved error handling"""
        print("Zimbabwe Tender Documentation System - Improved")
        print("=" * 80)
        print(f"Processing {len(self.entries)} sources")
        print("=" * 80)
        
        for i, (name, url) in enumerate(self.entries, 1):
            print(f"\n[{i}/{len(self.entries)}] {name}")
            print("-" * 60)
            
            tender_data = self.extract_tender_data(name, url)
            if tender_data:
                self.tender_data.append(tender_data)
                self.processed_count += 1
            
            # Brief delay to be respectful
            time.sleep(1)
        
        # Summary report
        print("\n" + "=" * 80)
        print("PROCESSING SUMMARY")
        print("=" * 80)
        print(f"Total sources: {len(self.entries)}")
        print(f"Successfully processed: {self.processed_count}")
        print(f"Broken URLs: {len(self.broken_urls)}")
        print(f"No tender content: {len(self.no_tender_content)}")
        print(f"Failed extractions: {len(self.failed_urls)}")
        
        if self.broken_urls:
            print(f"\nBroken URLs ({len(self.broken_urls)}):")
            for name, url, reason in self.broken_urls:
                print(f"  - {name}: {reason}")
        
        if self.no_tender_content:
            print(f"\nNo tender content ({len(self.no_tender_content)}):")
            for name, url, reason in self.no_tender_content:
                print(f"  - {name}: {reason}")
        
        print("=" * 80)
        return self.tender_data
    
    def create_summary_report(self, output_dir="zimbabwe_tenders"):
        """Create a comprehensive summary report with detailed tender information."""
        os.makedirs(output_dir, exist_ok=True)
        
        doc = Document()
        
        # Main Title
        title = doc.add_heading('Zimbabwe eGP Latest Tenders', 0)
        title.alignment = WD_ALIGN_PARAGRAPH.CENTER
        doc.add_paragraph(f"Report Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

        # --- Latest Tenders Summary Table ---
        doc.add_heading('Latest Tenders Summary', level=1)
        if self.tender_data and isinstance(self.tender_data[0], dict) and 'data' in self.tender_data[0] and isinstance(self.tender_data[0]['data'], list):
            tenders = self.tender_data[0]['data']
            table = doc.add_table(rows=1, cols=4)
            table.style = 'Table Grid'
            headers = table.rows[0].cells
            headers[0].text = 'Tender ID'
            headers[1].text = 'Tender Title'
            headers[2].text = 'Procuring Entity'
            headers[3].text = 'Closing Date'

            for tender in tenders:
                row = table.add_row().cells
                row[0].text = tender.get('tender_id', 'N/A')
                row[1].text = tender.get('title', 'N/A')
                row[2].text = tender.get('procuring_entity', 'N/A')
                row[3].text = tender.get('closing_date', 'N/A')
        else:
            doc.add_paragraph("No tender data was extracted to generate a summary table.")


        # --- Detailed Tender Information ---
        doc.add_heading('Detailed Tender Information', level=1)
        if self.tender_data and isinstance(self.tender_data[0], dict) and 'data' in self.tender_data[0] and isinstance(self.tender_data[0]['data'], list):
            tenders = self.tender_data[0]['data']
            for i, tender in enumerate(tenders, 1):
                doc.add_heading(f"Tender {i}: {tender.get('title', 'N/A')}", level=2)
                for key, value in tender.items():
                    doc.add_paragraph(f"**{key.replace('_', ' ').title()}:** {value}", style='List Bullet')
        else:
            doc.add_paragraph("No detailed tender information available.")


        # --- Processing Health Check ---
        doc.add_heading('Processing Health Check', level=1)
        health_summary = f"""
        - Total sources analyzed: {len(self.entries)}
        - Successfully processed: {self.processed_count}
        - Broken/moved URLs: {len(self.broken_urls)}
        - No tender content found: {len(self.no_tender_content)}
        - Failed extractions: {len(self.failed_urls)}
        """
        doc.add_paragraph(health_summary)

        if self.broken_urls or self.no_tender_content or self.failed_urls:
            doc.add_heading('URL Issues', level=2)
            issue_table = doc.add_table(rows=1, cols=3)
            issue_table.style = 'Table Grid'
            issue_headers = issue_table.rows[0].cells
            issue_headers[0].text = 'Source Name'
            issue_headers[1].text = 'Status'
            issue_headers[2].text = 'URL'

            for name, url, reason in self.broken_urls:
                row = issue_table.add_row().cells
                row[0].text = name
                row[1].text = 'BROKEN'
                row[2].text = url
            for name, url, reason in self.no_tender_content:
                row = issue_table.add_row().cells
                row[0].text = name
                row[1].text = 'NO_CONTENT'
                row[2].text = url
            for name, url, reason in self.failed_urls:
                row = issue_table.add_row().cells
                row[0].text = name
                row[1].text = 'FAILED'
                row[2].text = url

        # Save report
        filename = f"Zimbabwe_Tender_Data_{datetime.now().strftime('%Y%m%d_%H%M%S')}.docx"
        filepath = os.path.join(output_dir, filename)
        doc.save(filepath)
        
        print(f"Detailed tender report saved: {filename}")
        return filepath
    
    def run_analysis(self):
        """Run the complete analysis"""
        self.process_all_sources()
        
        if self.tender_data:
            print(f"\nFound {len(self.tender_data)} sources with extractable data")
            
            # Create summary report
            self.create_summary_report()
            
            return {
                'tender_data': self.tender_data,
                'broken_urls': self.broken_urls,
                'no_tender_content': self.no_tender_content,
                'failed_urls': self.failed_urls
            }
        else:
            print("No tender data could be extracted from any sources")
            self.create_summary_report()
            return None

def main():
    scraper = ImprovedZimbabweTenderScraper()
    results = scraper.run_analysis()
    return results

if __name__ == "__main__":
    main()
